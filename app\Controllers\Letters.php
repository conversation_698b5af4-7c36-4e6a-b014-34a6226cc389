<?php

namespace App\Controllers;

use App\Models\LettersModel;
use App\Models\employeesModel;
use App\Models\orgModel;

class Letters extends BaseController
{
    protected $lettersModel;
    protected $employeesModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        // Load required helpers
        helper(['form', 'url', 'security']);

        // Initialize models for database operations
        $this->lettersModel = new LettersModel();      // Handles letter-related operations
        $this->employeesModel = new employeesModel();  // Manages employee data
        $this->orgModel = new orgModel();              // Handles organization-related operations

        // Initialize session instance
        $this->session = session();
    }

    public function index()
    {
        return redirect()->to('letters/manage_letters');
    }

    public function manage_letters()
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to(base_url())->with('error', 'Please login first');
        }

        $org_id = $this->session->get('org_id');

        // Get all letters for the organization
        $letters = $this->lettersModel->where('org_id', $org_id)
                                    ->orderBy('created_at', 'DESC')
                                    ->findAll();

        // Add employee names to letters array
        foreach ($letters as &$letter) {
            $letter['created_by_name'] = $this->employeesModel->getEmployeeFullName($letter['created_by']);
        }

        // Count letters by status
        $pending_count = $this->lettersModel->where('org_id', $org_id)
                                          ->where('confirmation_status', 'pending')
                                          ->countAllResults();
        
        $approved_count = $this->lettersModel->where('org_id', $org_id)
                                           ->where('confirmation_status', 'approved')
                                           ->countAllResults();
        
        $rejected_count = $this->lettersModel->where('org_id', $org_id)
                                           ->where('confirmation_status', 'rejected')
                                           ->countAllResults();

        $data = [
            'title' => 'Letters Management',
            'menu' => 'letters',
            'letters' => $letters,
            'stats' => [
                'total' => count($letters),
                'pending' => $pending_count,
                'approved' => $approved_count,
                'rejected' => $rejected_count
            ]
        ];

        return view('letters/manage_letters', $data);
    }

    public function public_view($unique_code)
    {
        $letter = $this->lettersModel->where('unique_code', $unique_code)->first();

        if (!$letter || $letter['confirmation_status'] !== 'pending') {
            return redirect()->to('/')->with('error', 'Invalid or expired letter link');
        }

        // Get employee and organization details
        $db = \Config\Database::connect();
        $employee = $db->table('employees')
            ->select('employees.*, dakoii_org.name as org_name, dakoii_org.orglogo as org_logo, dakoii_org.id as org_id, positions.designation, groupings.name as group_name')
            ->join('dakoii_org', 'dakoii_org.id = employees.org_id', 'left')
            ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
            ->join('groupings', 'groupings.id = positions.group_id', 'left')
            ->where('employees.emp_id', $letter['created_by'])
            ->get()
            ->getRowArray();

        // Get organization details using orgModel
        $org = $this->orgModel->find($employee['org_id']);

        // Add view letter URL to the data
        $viewLetterUrl = base_url("letters/view_pdf/{$unique_code}");

        // Prepare organization logo URL
        $orgLogo = !empty($org['orglogo']) ? base_url($org['orglogo']) : base_url('public/assets/system_img/system-logo.png');

        $data = [
            'title' => 'Review Letter',
            'letter' => $letter,
            'employee' => $employee,
            'viewLetterUrl' => $viewLetterUrl,
            'orgName' => $org['name'],
            'orgLogo' => $orgLogo,
            'orgPhone' => $org['phones'],
            'orgEmail' => $org['emails'],
            'orgAddress' => $org['postal_address'],
            'signature' => $org['signature_filepath'],
            'signature_position' => $org['signature_position'],
            'signature_name' => $org['signature_name'],
            'stamp_filepath' => $org['stamp_filepath'],
            'approved_stamp_filepath' => $org['approved_stamp_filepath']
        ];

        return view('letters/public_view', $data);
    }

    public function view_pdf($unique_code)
    {
        try {
            $letter = $this->lettersModel->where('unique_code', $unique_code)->first();

            if (!$letter) {
                throw new \Exception('Letter not found');
            }

            // Get employee details
            $db = \Config\Database::connect();
            $employee = $db->table('employees')
                ->select('employees.*, dakoii_org.name as org_name, dakoii_org.orglogo as org_logo, dakoii_org.id as org_id, positions.designation, groupings.name as group_name')
                ->join('dakoii_org', 'dakoii_org.id = employees.org_id', 'left')
                ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
                ->join('groupings', 'groupings.id = positions.group_id', 'left')
                ->where('employees.emp_id', $letter['created_by'])
                ->get()
                ->getRowArray();

            $data = [
                'title' => 'View Letter',
                'letter' => $letter,
                'employee' => $employee
            ];

            // Return the view with letter content
            return view('letters/view_letter_content', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error viewing letter: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error viewing letter: ' . $e->getMessage());
        }
    }

    public function process_letter($unique_code)
    {
        try {
            $lettersModel = new \App\Models\LettersModel();
            $letter = $lettersModel->where('unique_code', $unique_code)->first();

            if (!$letter) {
                throw new \Exception('Letter not found');
            }

            if ($letter['confirmation_status'] !== 'pending') {
                throw new \Exception('Letter has already been processed');
            }

            // Get POST data
            $status = $this->request->getPost('status');
            $remarks = $this->request->getPost('remarks');
            $email = $this->request->getPost('email'); // Get email from form

            // Debug logging
            log_message('debug', 'Process Letter - Letter ID: ' . $letter['id'] .
                                ', Status: ' . ($status ?? 'null') .
                                ', Remarks: ' . ($remarks ?? 'null') .
                                ', Email: ' . ($email ?? 'null') .
                                ', Current Status: ' . $letter['confirmation_status']);

            // Validate status
            if (empty($status) || !in_array($status, ['approved', 'rejected'])) {
                throw new \Exception('Invalid status value: ' . ($status ?? 'null'));
            }

            if (empty($remarks)) {
                throw new \Exception('Remarks are required');
            }

            if (empty($email)) {
                throw new \Exception('Email is required');
            }

            // Update letter status
            $updateData = [
                'confirmation_status' => $status,
                'confirmation_remarks' => $remarks,
                'confirmed_at' => date('Y-m-d H:i:s'),
                'confirmed_by_email' => $email
            ];

            // Perform the update with validation skipped for partial updates
            $lettersModel->skipValidation(true);
            if (!$lettersModel->update($letter['id'], $updateData)) {
                $errors = $lettersModel->errors();
                log_message('error', 'Failed to update letter status. Letter ID: ' . $letter['id'] . '. Data: ' . json_encode($updateData) . '. Errors: ' . json_encode($errors));
                throw new \Exception('Failed to update letter status: ' . (is_array($errors) ? implode(', ', $errors) : 'Unknown error'));
            }

            // Log the successful update
            log_message('info', 'Letter status updated successfully. Letter ID: ' . $letter['id'] . '. New Status: ' . $status);

            // Send email notification to employee for both approved and rejected letters
            try {
                // Merge the updated data with the original letter data for email
                $updatedLetter = array_merge($letter, $updateData);

                if ($status === 'approved') {
                    $this->sendApprovalNotificationEmail($updatedLetter);
                } elseif ($status === 'rejected') {
                    $this->sendRejectionNotificationEmail($updatedLetter);
                }
            } catch (\Exception $e) {
                // Log email error but don't fail the approval/rejection process
                log_message('error', 'Failed to send ' . $status . ' email notification: ' . $e->getMessage());
            }

            return redirect()->to("/")
                           ->with('success', 'Letter has been ' . $status . ' successfully');

        } catch (\Exception $e) {
            log_message('error', 'Error processing letter: ' . $e->getMessage());
            return redirect()->back()
                           ->with('error', $e->getMessage());
        }
    }

    /**
     * Send approval notification email to employee
     *
     * @param array $letter Letter data
     * @throws \Exception
     */
    private function sendApprovalNotificationEmail($letter)
    {
        try {
            // Get employee details including email
            $employeesModel = new \App\Models\employeesModel();
            $employee = $employeesModel->find($letter['created_by']);

            if (!$employee) {
                throw new \Exception('Employee not found for letter ID: ' . $letter['id']);
            }

            // Check if employee has email address
            if (empty($employee['primary_email'])) {
                log_message('info', 'Employee ' . $employee['fname'] . ' ' . $employee['lname'] . ' (ID: ' . $employee['emp_id'] . ') has no email address. Skipping email notification.');
                return;
            }

            // Validate email format
            if (!filter_var($employee['primary_email'], FILTER_VALIDATE_EMAIL)) {
                log_message('warning', 'Employee ' . $employee['fname'] . ' ' . $employee['lname'] . ' (ID: ' . $employee['emp_id'] . ') has invalid email format: ' . $employee['primary_email']);
                return;
            }

            // Load email service
            $email = \Config\Services::email();

            // Set email parameters
            $email->setFrom('<EMAIL>', 'GovPSS System');
            $email->setTo($employee['primary_email']);
            $email->setSubject('Letter Approved - ' . $letter['subject']);

            // Prepare HTML message
            $message = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <title>Letter Approval Notification</title>
            </head>
            <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <div style='background-color: #28a745; color: white; padding: 20px; text-align: center;'>
                        <h2 style='margin: 0;'>Letter Approved</h2>
                    </div>
                    <div style='padding: 20px; background-color: #f9f9f9;'>
                        <p>Dear {$employee['fname']} {$employee['lname']},</p>
                        <p>We are pleased to inform you that your letter request has been <strong>approved</strong>.</p>

                        <div style='background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                            <p><strong>Letter Details:</strong></p>
                            <p><strong>Subject:</strong> {$letter['subject']}</p>
                            <p><strong>Letter Code:</strong> {$letter['letter_file_code']}</p>
                            <p><strong>Approved On:</strong> " . date('F j, Y \a\t g:i A', strtotime($letter['confirmed_at'])) . "</p>
                        </div>

                        <p>To access and download your approved letter, please log in to the Employee Portal:</p>
                        <p style='text-align: center;'>
                            <a href='" . base_url("employee_portal/dashboard") . "'
                               style='display: inline-block; padding: 12px 25px; background-color: #28a745;
                                      color: white; text-decoration: none; border-radius: 5px; font-weight: bold;'>
                                Access Employee Portal
                            </a>
                        </p>

                        <p style='margin-top: 20px;'>
                            <strong>Instructions:</strong><br>
                            1. Click the button above to access the Employee Portal<br>
                            2. Log in with your file number and password<br>
                            3. Navigate to 'Letters' section<br>
                            4. Find your approved letter and download it
                        </p>

                        <p style='font-size: 12px; margin-top: 20px;'>
                            If the button doesn't work, copy and paste this link into your browser:<br>
                            " . base_url("employee_portal/dashboard") . "
                        </p>
                    </div>
                    <div style='text-align: center; font-size: 12px; color: #666; margin-top: 20px;'>
                        <p>This is an automated message from GovPSS System. Please do not reply to this email.</p>
                    </div>
                </div>
            </body>
            </html>";

            $email->setMessage($message);

            // Send email
            if (!$email->send()) {
                $debugInfo = $email->printDebugger(['headers', 'subject', 'body']);
                log_message('error', 'Failed to send approval notification email to: ' . $employee['primary_email'] . '. Debug info: ' . print_r($debugInfo, true));
                throw new \Exception('Failed to send approval notification email');
            }

            log_message('info', 'Approval notification email sent successfully to: ' . $employee['primary_email'] . ' for letter ID: ' . $letter['id']);
            return true;

        } catch (\Exception $e) {
            log_message('error', 'Email notification error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send rejection notification email to employee
     *
     * @param array $letter Letter data
     * @throws \Exception
     */
    private function sendRejectionNotificationEmail($letter)
    {
        try {
            // Get employee details including email
            $employeesModel = new \App\Models\employeesModel();
            $employee = $employeesModel->find($letter['created_by']);

            if (!$employee) {
                throw new \Exception('Employee not found for letter ID: ' . $letter['id']);
            }

            // Check if employee has email address
            if (empty($employee['primary_email'])) {
                log_message('info', 'Employee ' . $employee['fname'] . ' ' . $employee['lname'] . ' (ID: ' . $employee['emp_id'] . ') has no email address. Skipping email notification.');
                return;
            }

            // Validate email format
            if (!filter_var($employee['primary_email'], FILTER_VALIDATE_EMAIL)) {
                log_message('warning', 'Employee ' . $employee['fname'] . ' ' . $employee['lname'] . ' (ID: ' . $employee['emp_id'] . ') has invalid email format: ' . $employee['primary_email']);
                return;
            }

            // Load email service
            $email = \Config\Services::email();

            // Set email parameters
            $email->setFrom('<EMAIL>', 'GovPSS System');
            $email->setTo($employee['primary_email']);
            $email->setSubject('Letter Rejected - ' . $letter['subject']);

            // Prepare HTML message
            $message = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <title>Letter Rejection Notification</title>
            </head>
            <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <div style='background-color: #dc3545; color: white; padding: 20px; text-align: center;'>
                        <h2 style='margin: 0;'>Letter Rejected</h2>
                    </div>
                    <div style='padding: 20px; background-color: #f9f9f9;'>
                        <p>Dear {$employee['fname']} {$employee['lname']},</p>
                        <p>We regret to inform you that your letter request has been <strong>rejected</strong>.</p>

                        <div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;'>
                            <p><strong>Letter Details:</strong></p>
                            <p><strong>Subject:</strong> {$letter['subject']}</p>
                            <p><strong>Letter Code:</strong> {$letter['letter_file_code']}</p>
                            <p><strong>Rejected On:</strong> " . date('F j, Y \a\t g:i A', strtotime($letter['confirmed_at'])) . "</p>
                            <p><strong>Rejection Reason:</strong> {$letter['confirmation_remarks']}</p>
                        </div>

                        <p>If you have any questions about this rejection or would like to submit a revised request, please contact your HR department or supervisor.</p>

                        <p style='text-align: center;'>
                            <a href='" . base_url("employee_portal/dashboard") . "'
                               style='display: inline-block; padding: 12px 25px; background-color: #6c757d;
                                      color: white; text-decoration: none; border-radius: 5px; font-weight: bold;'>
                                Access Employee Portal
                            </a>
                        </p>

                        <p style='font-size: 12px; margin-top: 20px;'>
                            If the button doesn't work, copy and paste this link into your browser:<br>
                            " . base_url("employee_portal/dashboard") . "
                        </p>
                    </div>
                    <div style='text-align: center; font-size: 12px; color: #666; margin-top: 20px;'>
                        <p>This is an automated message from GovPSS System. Please do not reply to this email.</p>
                    </div>
                </div>
            </body>
            </html>";

            $email->setMessage($message);

            // Send email
            if (!$email->send()) {
                $debugInfo = $email->printDebugger(['headers', 'subject', 'body']);
                log_message('error', 'Failed to send rejection notification email to: ' . $employee['primary_email'] . '. Debug info: ' . print_r($debugInfo, true));
                throw new \Exception('Failed to send rejection notification email');
            }

            log_message('info', 'Rejection notification email sent successfully to: ' . $employee['primary_email'] . ' for letter ID: ' . $letter['id']);
            return true;

        } catch (\Exception $e) {
            log_message('error', 'Email notification error: ' . $e->getMessage());
            throw $e;
        }
    }
}
